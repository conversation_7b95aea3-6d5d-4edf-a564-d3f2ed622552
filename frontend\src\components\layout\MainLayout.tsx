/**
 * 主布局组件 - 按照原型设计实现
 */

'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import NotificationContainer from '../NotificationContainer'
import GlobalLoadingOverlay from '../ui/GlobalLoadingOverlay'

interface LayoutProps {
  children: React.ReactNode
}

export default function MainLayout({ children }: LayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [currentTheme, setCurrentTheme] = useState('blue')
  const [themeDropdownOpen, setThemeDropdownOpen] = useState(false)

  // 主题配置
  const themes = [
    { id: 'blue', name: '蓝色主题', color: '#2563eb' },
    { id: 'green', name: '绿色主题', color: '#059669' },
    { id: 'purple', name: '紫色主题', color: '#7c3aed' },
    { id: 'orange', name: '橙色主题', color: '#ea580c' },
    { id: 'red', name: '红色主题', color: '#dc2626' },
  ]

  // 导航菜单配置
  const navigation = [
    {
      title: '主要功能',
      items: [
        {
          name: '首页仪表板',
          href: '/',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
          )
        },
        // {
        //   name: '视频生成(全自动)',
        //   href: '/generate',
        //   icon: (
        //     <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
        //       <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
        //     </svg>
        //   )
        // },
        {
          name: '视频生成(自备文案)',
          href: '/batch-generate',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          )
        },
        {
          name: '任务队列',
          href: '/tasks',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
              <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clipRule="evenodd"/>
            </svg>
          )
        }
      ]
    },
    {
      title: '资源管理',
      items: [        {
          name: '背景音乐',
          href: '/music',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.369 4.369 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
            </svg>
          )
        },        {
          name: '视频素材',
          href: '/videos',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"/>
            </svg>
          )
        },        {
          name: '提示词管理',
          href: '/prompts',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd"/>
            </svg>
          )
        },        {
          name: '账号管理',
          href: '/accounts',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
            </svg>
          )
        },        {
          name: '封面模板',
          href: '/covers',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
            </svg>
          )
        }
      ]
    },
    {
      title: '系统设置',
      items: [
        {
          name: '系统设置',
          href: '/settings',
          icon: (
            <svg className="nav-icon" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"/>
            </svg>
          )
        }
      ]
    }
  ]

  // 获取当前页面的面包屑
  const getBreadcrumb = () => {
    const currentNav = navigation
      .flatMap(group => group.items)
      .find(item => item.href === pathname)
    
    if (currentNav) {
      return ['首页', currentNav.name]
    }
    
    return ['首页', '仪表板']
  }

  // 主题切换
  const changeTheme = (themeId: string) => {
    setCurrentTheme(themeId)
    
    if (themeId === 'blue') {
      document.documentElement.removeAttribute('data-theme')
    } else {
      document.documentElement.setAttribute('data-theme', themeId)
    }
    
    localStorage.setItem('theme', themeId)
    setThemeDropdownOpen(false)
  }

  // 页面加载时恢复主题
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') || 'blue'
    setCurrentTheme(savedTheme)
    changeTheme(savedTheme)
  }, [])

  // 导航点击处理
  const handleNavClick = (href: string) => {
    router.push(href)
    setSidebarOpen(false)
  }

  const breadcrumb = getBreadcrumb()

  return (
    <>
      <NotificationContainer />
      
      {/* 主题样式 */}
      <style jsx global>{`
        :root {
          --theme-primary: #2563eb;
          --theme-primary-hover: #1d4ed8;
          --theme-primary-light: #eff6ff;
          --bg-primary: #f8fafc;
          --bg-secondary: #ffffff;
          --bg-tertiary: #f9fafb;
          --text-primary: #1f2937;
          --text-secondary: #6b7280;
          --text-tertiary: #374151;
          --border-primary: #e5e7eb;
          --border-secondary: #d1d5db;
        }

        [data-theme="green"] {
          --theme-primary: #059669;
          --theme-primary-hover: #047857;
          --theme-primary-light: #ecfdf5;
        }

        [data-theme="purple"] {
          --theme-primary: #7c3aed;
          --theme-primary-hover: #6d28d9;
          --theme-primary-light: #f3f4f6;
        }

        [data-theme="orange"] {
          --theme-primary: #ea580c;
          --theme-primary-hover: #dc2626;
          --theme-primary-light: #fff7ed;
        }

        [data-theme="red"] {
          --theme-primary: #dc2626;
          --theme-primary-hover: #b91c1c;
          --theme-primary-light: #fef2f2;
        }

        .app-container {
          display: flex;
          min-height: 100vh;
        }

        .sidebar {
          width: 260px;
          background: var(--bg-secondary);
          border-right: 1px solid var(--border-primary);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          position: fixed;
          height: 100vh;
          z-index: 1000;
          transition: transform 0.3s;
        }

        .logo-section {
          padding: 20px;
          border-bottom: 1px solid var(--border-primary);
          background: var(--theme-primary);
          color: white;
        }

        .logo-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .logo-subtitle {
          font-size: 12px;
          opacity: 0.8;
        }

        .nav-menu {
          padding: 16px 0;
          overflow-y: auto;
          height: calc(100vh - 140px);
        }

        .nav-group {
          margin-bottom: 24px;
        }

        .nav-group-title {
          padding: 0 20px 8px;
          font-size: 12px;
          font-weight: 600;
          color: var(--text-secondary);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .nav-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          color: var(--text-tertiary);
          text-decoration: none;
          transition: all 0.2s;
          border-left: 3px solid transparent;
          cursor: pointer;
        }

        .nav-item:hover {
          background: #f3f4f6;
          color: var(--theme-primary);
        }

        .nav-item.active {
          background: var(--theme-primary-light);
          color: var(--theme-primary);
          border-left-color: var(--theme-primary);
          font-weight: 500;
        }

        .nav-icon {
          width: 20px;
          height: 20px;
          margin-right: 12px;
          fill: currentColor;
        }

        .main-content {
          flex: 1;
          margin-left: 260px;
          display: flex;
          flex-direction: column;
        }

        .top-bar {
          background: var(--bg-secondary);
          border-bottom: 1px solid var(--border-primary);
          padding: 16px 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          position: sticky;
          top: 0;
          z-index: 100;
        }

        .breadcrumb {
          display: flex;
          align-items: center;
          color: var(--text-secondary);
          font-size: 14px;
        }

        .breadcrumb-item {
          color: var(--text-tertiary);
        }

        .breadcrumb-separator {
          margin: 0 8px;
          color: var(--border-secondary);
        }

        .top-actions {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .action-btn {
          padding: 8px 16px;
          border: 1px solid var(--border-secondary);
          background: var(--bg-secondary);
          color: var(--text-tertiary);
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          transition: all 0.2s;
        }

        .action-btn:hover {
          border-color: var(--theme-primary);
          color: var(--theme-primary);
        }

        .action-btn.primary {
          background: var(--theme-primary);
          color: white;
          border-color: var(--theme-primary);
        }

        .action-btn.primary:hover {
          background: var(--theme-primary-hover);
        }

        .theme-switcher {
          position: relative;
          margin-right: 12px;
        }

        .theme-btn {
          padding: 8px;
          border: 1px solid var(--border-secondary);
          background: var(--bg-secondary);
          color: var(--text-tertiary);
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .theme-btn:hover {
          border-color: var(--theme-primary);
          color: var(--theme-primary);
        }

        .theme-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          margin-top: 4px;
          background: var(--bg-secondary);
          border: 1px solid var(--border-primary);
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 1000;
          min-width: 120px;
          display: ${themeDropdownOpen ? 'block' : 'none'};
        }

        .theme-option {
          display: flex;
          align-items: center;
          padding: 8px 12px;
          cursor: pointer;
          transition: background 0.2s;
          font-size: 14px;
        }

        .theme-option:hover {
          background: var(--bg-primary);
        }

        .theme-option.active {
          background: var(--theme-primary-light);
          color: var(--theme-primary);
        }

        .theme-color {
          width: 16px;
          height: 16px;
          border-radius: 3px;
          margin-right: 8px;
          border: 1px solid var(--border-primary);
        }

        .page-content {
          flex: 1;
          padding: 24px;
          overflow-y: auto;
          background: var(--bg-primary);
        }

        .status-bar {
          background: var(--bg-tertiary);
          border-top: 1px solid var(--border-primary);
          padding: 8px 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
          color: var(--text-secondary);
        }

        .status-info {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .status-indicator {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .status-dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #059669;
        }

        .status-dot.warning {
          background: #d97706;
        }

        .status-dot.error {
          background: #dc2626;
        }

        @media (max-width: 768px) {
          .sidebar {
            transform: translateX(${sidebarOpen ? '0' : '-100%'});
          }

          .main-content {
            margin-left: 0;
          }

          .page-content {
            padding: 16px;
          }
        }
      `}</style>

      <div className="app-container">
        {/* 侧边栏 */}
        <div className="sidebar">
          {/* Logo区域 */}
          <div className="logo-section">
            <div className="logo-title">Reddit视频生成器</div>
            <div className="logo-subtitle">Story Video Generator</div>
          </div>

          {/* 导航菜单 */}
          <nav className="nav-menu">
            {navigation.map((group, groupIndex) => (
              <div key={groupIndex} className="nav-group">
                <div className="nav-group-title">{group.title}</div>
                {group.items.map((item, itemIndex) => (
                  <div
                    key={itemIndex}
                    className={`nav-item ${pathname === item.href ? 'active' : ''}`}
                    onClick={() => handleNavClick(item.href)}
                  >
                    {item.icon}
                    {item.name}
                  </div>
                ))}
              </div>
            ))}
          </nav>
        </div>

        {/* 主内容区域 */}
        <div className="main-content">
          {/* 顶部导航栏 */}
          <header className="top-bar">
            <div className="breadcrumb">
              {breadcrumb.map((crumb, index) => (
                <React.Fragment key={index}>
                  <span className="breadcrumb-item">{crumb}</span>
                  {index < breadcrumb.length - 1 && (
                    <span className="breadcrumb-separator">/</span>
                  )}
                </React.Fragment>
              ))}
            </div>

            <div className="top-actions">
              {/* 主题切换器 */}
              <div className="theme-switcher">
                <button 
                  className="theme-btn"
                  onClick={() => setThemeDropdownOpen(!themeDropdownOpen)}
                >
                  <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clipRule="evenodd"/>
                    <path d="M6 6h8v2H6V6zM6 10h8v2H6v-2z"/>
                  </svg>
                </button>
                <div className="theme-dropdown">
                  {themes.map((theme) => (
                    <div
                      key={theme.id}
                      className={`theme-option ${currentTheme === theme.id ? 'active' : ''}`}
                      onClick={() => changeTheme(theme.id)}
                    >
                      <div 
                        className="theme-color" 
                        style={{ background: theme.color }}
                      />
                      {theme.name}
                    </div>
                  ))}
                </div>
              </div>
              <button className="action-btn">帮助</button>
              {/* <button className="action-btn primary">新建任务</button> */}
            </div>
          </header>

          {/* 页面内容 */}
          <main className="page-content">
            {children}
          </main>

          {/* 状态栏 */}
          <footer className="status-bar">
            <div className="status-info">
              <div className="status-indicator">
                <div className="status-dot"></div>
                <span>系统运行正常</span>
              </div>
              <div className="status-indicator">
                <span>任务队列: 0</span>
              </div>
              <div className="status-indicator">
                <span>已完成: 15</span>
              </div>
            </div>
            <div className="status-info">
              <span>版本 v1.0.0</span>
              <span>最后更新: 2025-06-24</span>
            </div>
          </footer>
        </div>
      </div>
      
      {/* 全局Loading遮罩层 */}
      <GlobalLoadingOverlay />
    </>
  )
}
